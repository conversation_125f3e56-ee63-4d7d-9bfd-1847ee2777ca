#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include <filesystem>
#include "zexuan/plugin/simple_plugin_manager.hpp"
#include "zexuan/plugin/plugin_communication.hpp"
#include "zexuan/singleton/singleton_registry.hpp"
#include "spdlog/spdlog.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include "zexuan/thread_pool.hpp"
namespace fs = std::filesystem;

void printUsage(const char* programName) {
    std::cout << "Usage: " << programName << " <folder_path>" << std::endl;
    std::cout << "  folder_path: Path to the folder containing files to rename" << std::endl;
    std::cout << std::endl;
    std::cout << "Example: " << programName << " /path/to/comic/folder" << std::endl;
    std::cout << std::endl;
    std::cout << "Files will be renamed to format: YYYYMMDDHHMMSSMMM_UUID.ext" << std::endl;
    std::cout << "Example: 20240823133804123_c87398b4-0e7e-4ef5-9d8f-7e2dbde914cc.jpg" << std::endl;
}

// 使用 SimplePluginManager 替代自定义的 PluginLoader

int main(int argc, char* argv[]) {
    try {
        // 首先初始化系统管理器（必须在最开始）
        zexuan::SystemManager systemManager;
        BS::thread_pool threadpool__;
        // 检查命令行参数
        if (argc != 2) {
            printUsage(argv[0]);
            return 1;
        }

        std::string folderPath = argv[1];

        // 验证文件夹路径
        if (!fs::exists(folderPath)) {
            std::cerr << "Error: Folder does not exist: " << folderPath << std::endl;
            return 1;
        }

        if (!fs::is_directory(folderPath)) {
            std::cerr << "Error: Path is not a directory: " << folderPath << std::endl;
            return 1;
        }

        std::cout << "=== Comic Plugin Batch Renamer ===" << std::endl;
        std::cout << "Target folder: " << folderPath << std::endl;

        // SystemManager 已经自动注册了所有单例并初始化了插件系统
        std::cout << "Plugin system initialized by SystemManager" << std::endl;

        // 加载 ComicPlugin 来进行文件重命名
        std::string pluginPath = "libs/plugins/libcomic_plugin.so";

        if (!zexuan::plugin::loadPlugin(pluginPath)) {
            std::cerr << "Failed to load ComicPlugin from: " << pluginPath << std::endl;
            return 1;
        }

        std::cout << "ComicPlugin loaded successfully!" << std::endl;

        // 调试：打印主程序中的 MessageMediator 地址
        auto mainMediator = zexuan::getSingleton<zexuan::plugin::MessageMediator>();
        std::cout << "MainApp: MessageMediator address = " << mainMediator.get() << std::endl;

        // 检查插件是否已注册
        auto registeredParticipants = zexuan::plugin::getRegisteredParticipants();
        std::cout << "Registered participants: ";
        for (const auto& participant : registeredParticipants) {
            std::cout << participant << " ";
        }
        std::cout << std::endl;

        if (registeredParticipants.empty()) {
            std::cout << "Warning: No plugins registered. This may indicate a cross-library singleton issue." << std::endl;
        }

        std::cout << "Starting comic file renaming with ComicPlugin..." << std::endl;

        // 发送文件夹路径给 ComicPlugin 进行批量重命名
        std::cout << "1. Sending folder path to ComicPlugin..." << std::endl;
        zexuan::plugin::publishMessage("Folder path: " + folderPath, "MainApp", "ComicPlugin");

        // 发送开始处理命令
        std::cout << "2. Sending start processing command..." << std::endl;
        zexuan::plugin::publishMessage("start_processing", "MainApp", "ComicPlugin");

        std::cout << "File renaming completed!" << std::endl;

        // 显示最终状态
        auto finalParticipants = zexuan::plugin::getRegisteredParticipants();
        std::cout << "Final registered participants: ";
        for (const auto& participant : finalParticipants) {
            std::cout << participant << " ";
        }
        std::cout << std::endl;

        // SystemManager 析构时会自动关闭插件系统并清理所有单例
        std::cout << "SystemManager will handle cleanup automatically." << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}