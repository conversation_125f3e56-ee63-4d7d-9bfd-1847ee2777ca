#include "zexuan/singleton/singleton_registry.hpp"
#include "zexuan/logger.hpp"
#include "zexuan/config_loader.hpp"
#include "zexuan/plugin/message_mediator.hpp"
#include "zexuan/plugin/simple_plugin_manager.hpp"
#include "zexuan/plugin/plugin_communication.hpp"
#include <thread>
#include <chrono>
#include <iostream>

namespace zexuan {
// 注意：g_singleton_registry_accessor 现在在 core/src/zexuan/singleton_registry.cpp 中定义
void SystemInitializer::initialize() {
    auto& registry = singleton_registry();

    // 注册核心单例
    registry.registerSingleton<Logger>();
    registry.registerSingleton<ConfigLoader>();

    // 注册插件系统单例
    registry.registerSingleton<plugin::MessageMediator>();
    registry.registerSingleton<plugin::SimplePluginManager>();

    // 初始化插件系统
    plugin::initialize();

    std::cout << "SystemInitializer: 所有单例已注册，插件系统已初始化" << std::endl;
}

void SystemInitializer::cleanup() {
    std::cout << "SystemInitializer: 开始清理..." << std::endl;

    try {
        // 先关闭插件系统
        std::cout << "SystemInitializer: 正在关闭插件系统..." << std::endl;
        plugin::shutdown();
        std::cout << "SystemInitializer: 插件系统已关闭" << std::endl;

        // 再清理单例注册表（Logger 等核心服务最后清理）
        std::cout << "SystemInitializer: 正在清理单例注册表..." << std::endl;
        singleton_registry().clear();
        std::cout << "SystemInitializer: 所有单例已清理" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "SystemInitializer: 清理过程中发生异常: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "SystemInitializer: 清理过程中发生未知异常" << std::endl;
    }

    std::cout << "SystemInitializer: 清理完成" << std::endl;
}

} // namespace zexuan
