#pragma once

#include <memory>
#include <string>
#include <functional>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <mutex>
#include <any>
#include <chrono>
#include <algorithm>

namespace zexuan {
namespace core {

/**
 * 事件基类 - 所有事件的基础接口
 * 从 plugin 命名空间移动到 core 命名空间
 * 统一支持点对点和广播功能
 */
class Event {
public:
    virtual ~Event() = default;

    // 事件唯一标识符
    virtual std::string getEventType() const = 0;

    // 事件时间戳
    std::chrono::system_clock::time_point timestamp = std::chrono::system_clock::now();

    // 事件源标识
    std::string source;

    // 事件目标标识（空表示广播）
    std::string target;

    // 事件优先级 (数值越小优先级越高)
    int priority = 0;

    /**
     * 检查事件是否是发给指定目标的
     * @param targetName 目标名称
     * @return 是否匹配（空target表示广播，任何目标都匹配）
     */
    bool isTargetedTo(const std::string& targetName) const {
        return target.empty() || target == targetName;
    }

    /**
     * 检查是否为广播事件
     */
    bool isBroadcast() const {
        return target.empty();
    }

    /**
     * 设置事件目标
     */
    void setTarget(const std::string& targetName) {
        target = targetName;
    }

    /**
     * 获取事件目标
     */
    const std::string& getTarget() const {
        return target;
    }
};

/**
 * 事件处理器接口
 */
class EventHandler {
public:
    virtual ~EventHandler() = default;
    virtual void handleEvent(std::shared_ptr<Event> event) = 0;
    virtual std::string getHandlerId() const = 0;
};

/**
 * 类型化事件处理器
 */
template<typename EventType>
class TypedEventHandler : public EventHandler {
public:
    using HandlerFunc = std::function<void(std::shared_ptr<EventType>)>;
    
    TypedEventHandler(const std::string& id, HandlerFunc handler)
        : handlerId_(id), handler_(handler) {}
    
    void handleEvent(std::shared_ptr<Event> event) override {
        auto typedEvent = std::dynamic_pointer_cast<EventType>(event);
        if (typedEvent) {
            handler_(typedEvent);
        }
    }
    
    std::string getHandlerId() const override {
        return handlerId_;
    }
    
private:
    std::string handlerId_;
    HandlerFunc handler_;
};

/**
 * 通用总线中介者基类
 * 提供事件发布订阅的基础实现
 * 子类只需要实现特定的业务逻辑
 */
class BusMediator {
public:
    virtual ~BusMediator() = default;

    // === 核心事件总线接口 ===

    /**
     * 发布事件到总线
     * @param event 事件对象
     */
    virtual void publishEvent(std::shared_ptr<Event> event) {
        distributeEvent(event);
    }

    /**
     * 发布事件到指定参与者（点对点发送）
     * @param event 事件对象
     * @param targetParticipant 目标参与者ID，为空则广播
     */
    virtual void publishEventToTarget(std::shared_ptr<Event> event, const std::string& targetParticipant) {
        // 设置事件的目标
        event->setTarget(targetParticipant);

        if (targetParticipant.empty()) {
            // 空目标表示广播
            distributeEvent(event);
        } else {
            // 点对点发送
            distributeEventToTarget(event, targetParticipant);
        }
    }

    /**
     * 订阅特定类型的事件
     * @param eventType 事件类型
     * @param handler 事件处理器
     */
    virtual void subscribeEvent(const std::string& eventType, std::shared_ptr<EventHandler> handler) {
        std::lock_guard<std::mutex> lock(mutex_);
        eventSubscribers_[eventType].push_back(handler);
    }

    /**
     * 取消订阅事件
     * @param eventType 事件类型
     * @param handlerId 处理器ID
     */
    virtual void unsubscribeEvent(const std::string& eventType, const std::string& handlerId) {
        std::lock_guard<std::mutex> lock(mutex_);
        auto it = eventSubscribers_.find(eventType);
        if (it != eventSubscribers_.end()) {
            auto& handlers = it->second;
            handlers.erase(
                std::remove_if(handlers.begin(), handlers.end(),
                    [&handlerId](const std::shared_ptr<EventHandler>& handler) {
                        return handler && handler->getHandlerId() == handlerId;
                    }),
                handlers.end()
            );

            if (handlers.empty()) {
                eventSubscribers_.erase(it);
            }
        }
    }

    // === 参与者管理接口 ===

    /**
     * 注册参与者（插件、服务等）
     * @param participantId 参与者ID
     */
    virtual void registerParticipant(const std::string& participantId) {
        std::lock_guard<std::mutex> lock(mutex_);
        registeredParticipants_.insert(participantId);
    }

    /**
     * 注销参与者
     * @param participantId 参与者ID
     */
    virtual void unregisterParticipant(const std::string& participantId) {
        std::lock_guard<std::mutex> lock(mutex_);
        registeredParticipants_.erase(participantId);
    }

    /**
     * 检查参与者是否已注册
     * @param participantId 参与者ID
     * @return 是否已注册
     */
    virtual bool isParticipantRegistered(const std::string& participantId) const {
        std::lock_guard<std::mutex> lock(mutex_);
        return registeredParticipants_.find(participantId) != registeredParticipants_.end();
    }

    /**
     * 获取已注册的参与者列表
     * @return 参与者ID列表
     */
    virtual std::vector<std::string> getRegisteredParticipants() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return std::vector<std::string>(registeredParticipants_.begin(), registeredParticipants_.end());
    }

protected:
    /**
     * 分发事件给订阅者（广播）
     * 基类提供默认实现，子类可以重写以实现特定逻辑
     * @param event 事件对象
     */
    virtual void distributeEvent(std::shared_ptr<Event> event) {
        // 先获取订阅者列表的副本，避免在调用handleEvent时持有锁
        std::vector<std::shared_ptr<EventHandler>> handlers;

        {
            std::lock_guard<std::mutex> lock(mutex_);
            auto it = eventSubscribers_.find(event->getEventType());
            if (it != eventSubscribers_.end()) {
                handlers = it->second;  // 复制订阅者列表
            }
        } // 锁在这里释放

        // 在没有锁的情况下调用事件处理器，避免死锁
        for (auto& handler : handlers) {
            if (handler) {
                handler->handleEvent(event);
            }
        }
    }

    /**
     * 分发事件给指定参与者（点对点发送）
     * @param event 事件对象
     * @param targetParticipant 目标参与者ID
     */
    virtual void distributeEventToTarget(std::shared_ptr<Event> event, const std::string& targetParticipant) {
        // 先获取订阅者列表的副本，避免在调用handleEvent时持有锁
        std::vector<std::shared_ptr<EventHandler>> handlers;

        {
            std::lock_guard<std::mutex> lock(mutex_);

            // 检查目标参与者是否已注册
            if (registeredParticipants_.find(targetParticipant) == registeredParticipants_.end()) {
                return; // 目标参与者不存在，忽略消息
            }

            auto it = eventSubscribers_.find(event->getEventType());
            if (it != eventSubscribers_.end()) {
                // 只选择目标参与者的处理器
                for (auto& handler : it->second) {
                    if (handler && isHandlerForParticipant(handler, targetParticipant)) {
                        handlers.push_back(handler);
                    }
                }
            }
        } // 锁在这里释放

        // 在没有锁的情况下调用事件处理器，避免死锁
        for (auto& handler : handlers) {
            if (handler) {
                handler->handleEvent(event);
            }
        }
    }

    /**
     * 检查处理器是否属于指定参与者
     * 基类提供简单的ID匹配实现，子类可以重写以实现更复杂的逻辑
     * @param handler 事件处理器
     * @param participantId 参与者ID
     * @return 是否匹配
     */
    virtual bool isHandlerForParticipant(std::shared_ptr<EventHandler> handler, const std::string& participantId) {
        // 简单的ID前缀匹配：处理器ID通常以参与者ID开头
        return handler->getHandlerId().find(participantId) == 0;
    }

    // 事件订阅管理
    std::unordered_map<std::string, std::vector<std::shared_ptr<EventHandler>>> eventSubscribers_;

    // 已注册的参与者
    std::unordered_set<std::string> registeredParticipants_;

    // 线程安全保护
    mutable std::mutex mutex_;
};

} // namespace core
} // namespace zexuan
